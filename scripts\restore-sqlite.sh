#!/bin/bash

# SQLite 数据库恢复脚本
# 使用方法: ./scripts/restore-sqlite.sh [备份文件路径]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

# 配置
DB_PATH="./data/production.db"
BACKUP_DIR="./backups"

# 显示使用说明
show_usage() {
    echo -e "${BLUE}SQLite 数据库恢复工具${NC}"
    echo ""
    echo "使用方法:"
    echo "  $0 [备份文件路径]"
    echo ""
    echo "示例:"
    echo "  $0 ./backups/youpin_backup_20240101_120000.db.gz"
    echo "  $0  # 交互式选择备份文件"
    echo ""
}

# 列出可用的备份文件
list_backups() {
    echo -e "${BLUE}📋 可用的备份文件:${NC}"
    if ls $BACKUP_DIR/youpin_backup_*.db.gz 1> /dev/null 2>&1; then
        ls -lht $BACKUP_DIR/youpin_backup_*.db.gz | nl
    else
        echo -e "${YELLOW}  无可用备份文件${NC}"
        exit 1
    fi
}

# 交互式选择备份文件
select_backup() {
    list_backups
    echo ""
    echo -e "${YELLOW}请输入要恢复的备份文件编号:${NC}"
    read -r selection
    
    # 获取选中的文件
    backup_file=$(ls -t $BACKUP_DIR/youpin_backup_*.db.gz | sed -n "${selection}p")
    
    if [ -z "$backup_file" ]; then
        echo -e "${RED}❌ 无效的选择${NC}"
        exit 1
    fi
    
    echo "$backup_file"
}

# 恢复数据库
restore_database() {
    local backup_file="$1"
    
    echo -e "${BLUE}🔄 开始恢复数据库...${NC}"
    echo -e "  📁 备份文件: $backup_file"
    echo -e "  🎯 目标位置: $DB_PATH"
    
    # 确认操作
    echo -e "${YELLOW}⚠️ 此操作将覆盖当前数据库，是否继续? (y/N)${NC}"
    read -r confirm
    
    if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
        echo -e "${YELLOW}操作已取消${NC}"
        exit 0
    fi
    
    # 备份当前数据库
    if [ -f "$DB_PATH" ]; then
        echo -e "${BLUE}💾 备份当前数据库...${NC}"
        current_backup="$BACKUP_DIR/current_backup_$(date +%Y%m%d_%H%M%S).db"
        cp "$DB_PATH" "$current_backup"
        echo -e "${GREEN}  当前数据库已备份到: $current_backup${NC}"
    fi
    
    # 创建数据目录
    mkdir -p "$(dirname "$DB_PATH")"
    
    # 解压并恢复
    echo -e "${BLUE}📦 解压备份文件...${NC}"
    if [[ "$backup_file" == *.gz ]]; then
        gunzip -c "$backup_file" > "$DB_PATH"
    else
        cp "$backup_file" "$DB_PATH"
    fi
    
    # 验证恢复结果
    if [ -f "$DB_PATH" ]; then
        echo -e "${GREEN}✅ 数据库恢复成功!${NC}"
        
        # 显示数据库信息
        if command -v sqlite3 &> /dev/null; then
            echo -e "${BLUE}📊 数据库信息:${NC}"
            echo -e "  文件大小: $(du -h "$DB_PATH" | cut -f1)"
            echo -e "  用户数量: $(sqlite3 "$DB_PATH" "SELECT COUNT(*) FROM users;" 2>/dev/null || echo "无法获取")"
        fi
    else
        echo -e "${RED}❌ 数据库恢复失败${NC}"
        exit 1
    fi
}

# 主函数
main() {
    echo -e "${GREEN}🔄 SQLite 数据库恢复工具${NC}"
    echo "=================================="
    
    # 检查参数
    if [ $# -eq 0 ]; then
        # 交互式模式
        backup_file=$(select_backup)
    elif [ $# -eq 1 ]; then
        # 指定文件模式
        backup_file="$1"
        
        # 检查文件是否存在
        if [ ! -f "$backup_file" ]; then
            echo -e "${RED}❌ 备份文件不存在: $backup_file${NC}"
            show_usage
            exit 1
        fi
    else
        show_usage
        exit 1
    fi
    
    restore_database "$backup_file"
    
    echo -e "${GREEN}🎉 恢复任务完成!${NC}"
    echo -e "${YELLOW}💡 建议重启应用以确保更改生效${NC}"
}

# 执行主函数
main "$@"
