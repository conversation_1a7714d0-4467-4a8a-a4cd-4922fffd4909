'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { useAuth } from '@/contexts/AuthContext';

export default function SubscriptionPage() {
  const [plans, setPlans] = useState([]);
  const [loading, setLoading] = useState(true);
  const [paymentLoading, setPaymentLoading] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState(null);
  
  const { user, isAuthenticated, getSubscriptionInfo } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/login');
      return;
    }
    fetchPlans();
  }, [isAuthenticated, router]);

  const fetchPlans = async () => {
    try {
      const response = await fetch('/api/subscription/plans');
      const data = await response.json();
      setPlans(data.plans || []);
    } catch (error) {
      console.error('获取订阅计划失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSubscribe = async (planId, paymentMethod) => {
    // 如果是二维码支付，直接跳转到二维码页面
    if (paymentMethod === 'qrcode') {
      router.push(`/payment/qrcode?plan=${planId}`);
      return;
    }

    setPaymentLoading(true);
    setSelectedPlan(planId);

    try {
      const token = localStorage.getItem('auth_token');
      const response = await fetch('/api/payment/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          planType: planId,
          paymentMethod
        })
      });

      const data = await response.json();

      if (response.ok) {
        // 跳转到支付页面
        window.open(data.payment.paymentUrl, '_blank');
      } else {
        alert('创建支付订单失败: ' + data.error);
      }
    } catch (error) {
      alert('支付失败: ' + error.message);
    } finally {
      setPaymentLoading(false);
      setSelectedPlan(null);
    }
  };

  const subscriptionInfo = getSubscriptionInfo();

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-4 border-blue-600 border-t-transparent"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl mx-auto">
        {/* 头部 */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-4">
            选择订阅计划
          </h1>
          <p className="text-xl text-gray-600">
            解锁完整功能，享受专业的饰品监控服务
          </p>
        </div>

        {/* 当前订阅状态 */}
        {subscriptionInfo.hasSubscription && (
          <div className="bg-white rounded-xl shadow-lg p-6 mb-8">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">当前订阅状态</h2>
            <div className="flex items-center justify-between">
              <div>
                <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                  subscriptionInfo.isActive 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-red-100 text-red-800'
                }`}>
                  {subscriptionInfo.isActive ? '✅ 活跃' : '❌ 已过期'}
                </span>
                <p className="text-gray-600 mt-2">
                  计划类型: {subscriptionInfo.planType === 'monthly' ? '月度会员' : '年度会员'}
                </p>
                <p className="text-gray-600">
                  到期时间: {new Date(subscriptionInfo.endDate).toLocaleDateString()}
                </p>
                {subscriptionInfo.isActive && (
                  <p className="text-gray-600">
                    剩余天数: {subscriptionInfo.daysRemaining} 天
                  </p>
                )}
              </div>
            </div>
          </div>
        )}

        {/* 订阅计划 */}
        <div className="grid md:grid-cols-2 gap-8">
          {plans.map((plan) => (
            <div 
              key={plan.id} 
              className={`bg-white rounded-2xl shadow-xl overflow-hidden ${
                plan.popular ? 'ring-4 ring-purple-500 ring-opacity-50' : ''
              }`}
            >
              {plan.popular && (
                <div className="bg-gradient-to-r from-purple-500 to-pink-500 text-white text-center py-2 text-sm font-medium">
                  🔥 最受欢迎
                </div>
              )}
              
              <div className="p-8">
                <div className="text-center mb-6">
                  <h3 className="text-2xl font-bold text-gray-800">{plan.name}</h3>
                  <p className="text-gray-600 mt-2">{plan.description}</p>
                  
                  <div className="mt-4">
                    <span className="text-4xl font-bold text-gray-800">¥{plan.price}</span>
                    <span className="text-gray-600">/{plan.id === 'monthly' ? '月' : '年'}</span>
                    {plan.originalPrice && (
                      <div className="text-sm text-gray-500 mt-1">
                        <span className="line-through">原价 ¥{plan.originalPrice}</span>
                        <span className="text-green-600 ml-2">省 {plan.discount}</span>
                      </div>
                    )}
                  </div>
                </div>

                <div className="space-y-3 mb-8">
                  {plan.features.map((feature, index) => (
                    <div key={index} className="flex items-center">
                      <span className="text-green-500 mr-3">✓</span>
                      <span className="text-gray-700">{feature}</span>
                    </div>
                  ))}
                </div>

                <div className="space-y-3">
                  <button
                    onClick={() => handleSubscribe(plan.id, 'qrcode')}
                    className="w-full bg-gradient-to-r from-purple-600 to-pink-600 text-white py-3 px-6 rounded-lg font-medium hover:from-purple-700 hover:to-pink-700 transition-all duration-200 shadow-lg"
                  >
                    📱 扫码支付（支付宝/微信）
                  </button>

                  <div className="text-center">
                    <p className="text-xs text-gray-500 mb-2">或选择其他支付方式</p>
                    <div className="flex space-x-2">
                      <button
                        // onClick={() => handleSubscribe(plan.id, 'alipay')}
                        disabled={paymentLoading && selectedPlan === plan.id}
                        className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg text-sm hover:bg-blue-700 transition-colors disabled:opacity-50"
                      >
                        {paymentLoading && selectedPlan === plan.id ? '处理中...' : '💰 支付宝'}
                      </button>

                      <button
                        // onClick={() => handleSubscribe(plan.id, 'wechat')}
                        disabled={paymentLoading && selectedPlan === plan.id}
                        className="flex-1 bg-green-600 text-white py-2 px-4 rounded-lg text-sm hover:bg-green-700 transition-colors disabled:opacity-50"
                      >
                        {paymentLoading && selectedPlan === plan.id ? '处理中...' : '💚 微信'}
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* 导航链接 */}
        <div className="text-center mt-12 space-x-6">
          <button
            onClick={() => router.push('/dashboard')}
            className="text-blue-600 hover:text-blue-500 transition-colors"
          >
            ← 返回仪表板
          </button>
          <Link href="/profile">
            <button className="text-purple-600 hover:text-purple-500 transition-colors">
              个人中心 →
            </button>
          </Link>
        </div>
      </div>
    </div>
  );
}
