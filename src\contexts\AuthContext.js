'use client';

import { createContext, useContext, useState, useEffect } from 'react';

const AuthContext = createContext();

export function useAuth() {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

export function AuthProvider({ children }) {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [token, setToken] = useState(null);

  // 初始化时检查本地存储的token
  useEffect(() => {
    const savedToken = localStorage.getItem('auth_token');
    const savedUser = localStorage.getItem('auth_user');
    
    if (savedToken && savedUser) {
      setToken(savedToken);
      setUser(JSON.parse(savedUser));
    }
    setLoading(false);
  }, []);

  // 登录函数
  const login = async (email, password) => {
    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || '登录失败');
      }

      // 保存token和用户信息
      setToken(data.token);
      setUser(data.user);
      localStorage.setItem('auth_token', data.token);
      localStorage.setItem('auth_user', JSON.stringify(data.user));

      return { success: true, user: data.user };
    } catch (error) {
      return { success: false, error: error.message };
    }
  };

  // 注册函数
  const register = async (email, password, username) => {
    try {
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password, username }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || '注册失败');
      }

      return { success: true, user: data.user };
    } catch (error) {
      return { success: false, error: error.message };
    }
  };

  // 登出函数
  const logout = () => {
    setUser(null);
    setToken(null);
    localStorage.removeItem('auth_token');
    localStorage.removeItem('auth_user');
  };

  // 获取用户详细信息
  const fetchUserProfile = async () => {
    if (!token) return null;

    try {
      const response = await fetch('/api/user/profile', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setUser(data.user);
        localStorage.setItem('auth_user', JSON.stringify(data.user));
        return data.user;
      }
    } catch (error) {
      console.error('获取用户信息失败:', error);
    }
    return null;
  };

  // 检查是否有有效订阅
  const hasValidSubscription = () => {
    if (!user?.subscriptions || user.subscriptions.length === 0) {
      return false;
    }

    const activeSubscription = user.subscriptions.find(
      sub => sub.status === 'active' && new Date(sub.endDate) > new Date()
    );

    return !!activeSubscription;
  };

  // 获取订阅状态信息
  const getSubscriptionInfo = () => {
    if (!user?.subscriptions || user.subscriptions.length === 0) {
      return { hasSubscription: false };
    }

    const activeSubscription = user.subscriptions.find(
      sub => sub.status === 'active' && new Date(sub.endDate) > new Date()
    );

    if (!activeSubscription) {
      return { hasSubscription: false };
    }

    const endDate = new Date(activeSubscription.endDate);
    const now = new Date();
    const daysRemaining = Math.ceil((endDate - now) / (1000 * 60 * 60 * 24));

    return {
      hasSubscription: true,
      isActive: true,
      planType: activeSubscription.planType,
      endDate: activeSubscription.endDate,
      daysRemaining: Math.max(0, daysRemaining)
    };
  };

  const value = {
    user,
    token,
    loading,
    login,
    register,
    logout,
    fetchUserProfile,
    hasValidSubscription,
    getSubscriptionInfo,
    isAuthenticated: !!user
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}
