#!/bin/bash

# 宝塔面板快速配置脚本
# 用于生成安全的配置文件
# 使用方法: ./scripts/baota-setup.sh

set -e

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

echo -e "${GREEN}🔧 宝塔面板配置生成器${NC}"
echo "=================================="

# 生成随机密钥
generate_secret() {
    openssl rand -base64 32 2>/dev/null || node -e "console.log(require('crypto').randomBytes(32).toString('base64'))" 2>/dev/null || echo "$(date +%s)$(shuf -i 1000-9999 -n 1)$(whoami)" | base64
}

# 获取用户输入
get_user_input() {
    echo -e "${BLUE}📝 请输入配置信息:${NC}"
    echo ""
    
    # 域名配置
    read -p "请输入你的域名 (例: yourdomain.com): " DOMAIN
    if [ -z "$DOMAIN" ]; then
        DOMAIN="localhost"
        echo -e "${YELLOW}⚠️ 使用默认域名: localhost${NC}"
    fi
    
    # 是否使用 HTTPS
    read -p "是否使用 HTTPS? (y/N): " USE_HTTPS
    if [[ "$USE_HTTPS" =~ ^[Yy]$ ]]; then
        PROTOCOL="https"
    else
        PROTOCOL="http"
    fi
    
    # 端口配置
    read -p "应用运行端口 (默认 3000): " APP_PORT
    if [ -z "$APP_PORT" ]; then
        APP_PORT="3000"
    fi
    
    echo ""
}

# 生成环境配置文件
generate_env_config() {
    echo -e "${BLUE}🔐 生成安全配置...${NC}"
    
    # 生成强密钥
    JWT_SECRET=$(generate_secret)
    NEXTAUTH_SECRET=$(generate_secret)
    
    # 创建生产环境配置
    cat > .env.production << EOF
# 生产环境配置文件
# 由宝塔配置脚本自动生成于 $(date)

# ===========================================
# 数据库配置
# ===========================================
DATABASE_URL="file:./data/production.db"

# ===========================================
# 安全配置 (已自动生成强密钥)
# ===========================================
JWT_SECRET="$JWT_SECRET"
NEXTAUTH_SECRET="$NEXTAUTH_SECRET"

# ===========================================
# 应用配置
# ===========================================
NEXT_PUBLIC_BASE_URL="$PROTOCOL://$DOMAIN"
NEXTAUTH_URL="$PROTOCOL://$DOMAIN"
NODE_ENV="production"
PORT="$APP_PORT"

# ===========================================
# 日志配置
# ===========================================
LOG_LEVEL="info"
ENABLE_DETAILED_LOGS="false"

# ===========================================
# 性能配置
# ===========================================
DATABASE_POOL_SIZE="10"
API_RATE_LIMIT="100"
EOF
    
    echo -e "${GREEN}✅ 环境配置文件已生成: .env.production${NC}"
}

# 生成 PM2 配置
generate_pm2_config() {
    echo -e "${BLUE}⚙️ 生成 PM2 配置...${NC}"
    
    cat > ecosystem.config.js << EOF
// PM2 配置文件 - 宝塔面板环境
// 由配置脚本自动生成于 $(date)

module.exports = {
  apps: [
    {
      name: 'youpin-sentinel',
      script: 'npm',
      args: 'start',
      cwd: '/www/wwwroot/youpin-sentinel',
      instances: 1, // 宝塔环境建议使用单实例
      exec_mode: 'fork',
      env: {
        NODE_ENV: 'production',
        PORT: $APP_PORT
      },
      
      // 日志配置
      log_file: './logs/combined.log',
      out_file: './logs/out.log',
      error_file: './logs/error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      
      // 进程管理
      max_memory_restart: '512M', // 轻量服务器内存限制
      min_uptime: '10s',
      max_restarts: 10,
      
      // 监控配置
      watch: false,
      autorestart: true,
      time: true,
      merge_logs: true
    }
  ]
};
EOF
    
    echo -e "${GREEN}✅ PM2 配置文件已生成: ecosystem.config.js${NC}"
}

# 生成 Nginx 配置提示
generate_nginx_guide() {
    echo -e "${BLUE}🌐 生成 Nginx 配置指南...${NC}"
    
    cat > nginx-config-guide.txt << EOF
宝塔面板 Nginx 配置指南
生成时间: $(date)
域名: $DOMAIN
协议: $PROTOCOL
端口: $APP_PORT

=== 配置步骤 ===

1. 在宝塔面板添加网站:
   - 域名: $DOMAIN
   - 不创建数据库
   - 不创建 FTP

2. 配置反向代理:
   - 进入网站设置 → 反向代理
   - 代理名称: youpin-sentinel
   - 目标URL: http://127.0.0.1:$APP_PORT
   - 发送域名: \$host

3. SSL 证书配置 (如果使用 HTTPS):
   - 进入网站设置 → SSL
   - 选择 Let's Encrypt
   - 申请免费证书
   - 开启强制 HTTPS

4. 高级配置 (可选):
   - 复制 nginx-baota.conf 中的配置
   - 替换网站配置文件中的相应部分

=== 测试访问 ===

健康检查: $PROTOCOL://$DOMAIN/api/health
管理后台: $PROTOCOL://$DOMAIN

默认管理员:
邮箱: <EMAIL>
密码: Admin123456 (请首次登录后修改)
EOF
    
    echo -e "${GREEN}✅ Nginx 配置指南已生成: nginx-config-guide.txt${NC}"
}

# 生成部署检查清单
generate_checklist() {
    echo -e "${BLUE}📋 生成部署检查清单...${NC}"
    
    cat > deployment-checklist.md << EOF
# 🚀 宝塔面板部署检查清单

生成时间: $(date)

## ✅ 部署前检查

- [ ] 阿里云轻量服务器已购买并配置
- [ ] 宝塔面板已安装 (版本 7.0+)
- [ ] 域名已解析到服务器 IP
- [ ] 安全组已开放 80、443、8888 端口

## ✅ 软件环境检查

- [ ] Node.js 已安装 (版本 18+)
- [ ] PM2 管理器已安装
- [ ] Nginx 已安装并运行
- [ ] 文件管理器可正常使用

## ✅ 项目部署检查

- [ ] 项目文件已上传到 /www/wwwroot/youpin-sentinel
- [ ] .env.production 配置文件已创建
- [ ] JWT_SECRET 和 NEXTAUTH_SECRET 已修改
- [ ] 依赖包已安装 (npm install)
- [ ] 数据库已初始化 (npx prisma db push)
- [ ] 项目已构建 (npm run build)
- [ ] 超级管理员已创建

## ✅ 服务配置检查

- [ ] PM2 进程已启动
- [ ] 应用运行在端口 $APP_PORT
- [ ] 网站已在宝塔面板添加
- [ ] 反向代理已配置
- [ ] SSL 证书已申请 (如使用 HTTPS)

## ✅ 功能测试检查

- [ ] 健康检查接口正常: $PROTOCOL://$DOMAIN/api/health
- [ ] 网站首页可访问: $PROTOCOL://$DOMAIN
- [ ] 管理员登录正常
- [ ] 用户注册功能正常
- [ ] 支付功能测试正常

## ✅ 安全配置检查

- [ ] 默认管理员密码已修改
- [ ] JWT 密钥已更换为强密钥
- [ ] 文件权限已正确设置
- [ ] 敏感文件访问已禁止

## ✅ 维护配置检查

- [ ] 数据库备份计划已设置
- [ ] 应用日志监控已配置
- [ ] 系统资源监控已启用
- [ ] 更新流程已了解

## 🔧 常用管理命令

\`\`\`bash
# 查看应用状态
pm2 status

# 查看应用日志
pm2 logs youpin-sentinel

# 重启应用
pm2 restart youpin-sentinel

# 手动备份数据库
cd /www/wwwroot/youpin-sentinel && ./scripts/backup-sqlite.sh
\`\`\`

## 📞 技术支持

如遇问题，请检查:
1. PM2 进程状态
2. Nginx 错误日志
3. 应用运行日志
4. 系统资源使用情况
EOF
    
    echo -e "${GREEN}✅ 部署检查清单已生成: deployment-checklist.md${NC}"
}

# 显示配置摘要
show_summary() {
    echo ""
    echo -e "${GREEN}🎉 配置生成完成！${NC}"
    echo "=================================="
    echo -e "${BLUE}📋 配置摘要:${NC}"
    echo -e "  • 域名: $DOMAIN"
    echo -e "  • 协议: $PROTOCOL"
    echo -e "  • 端口: $APP_PORT"
    echo -e "  • JWT 密钥: ${GREEN}已生成强密钥${NC}"
    echo ""
    echo -e "${BLUE}📁 生成的文件:${NC}"
    echo -e "  • .env.production (环境配置)"
    echo -e "  • ecosystem.config.js (PM2 配置)"
    echo -e "  • nginx-config-guide.txt (Nginx 指南)"
    echo -e "  • deployment-checklist.md (部署清单)"
    echo ""
    echo -e "${YELLOW}🚀 下一步操作:${NC}"
    echo -e "  1. 运行部署脚本: ${GREEN}./scripts/deploy-baota.sh${NC}"
    echo -e "  2. 按照 nginx-config-guide.txt 配置网站"
    echo -e "  3. 使用 deployment-checklist.md 检查部署"
    echo ""
}

# 主函数
main() {
    get_user_input
    generate_env_config
    generate_pm2_config
    generate_nginx_guide
    generate_checklist
    show_summary
}

# 执行主函数
main "$@"
