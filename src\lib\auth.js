import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-change-in-production';
const JWT_EXPIRES_IN = '10d'; // 10天登录保持

// 密码加密
export async function hashPassword(password) {
  return await bcrypt.hash(password, 12);
}

// 密码验证
export async function verifyPassword(password, hashedPassword) {
  return await bcrypt.compare(password, hashedPassword);
}

// 生成JWT token
export function generateToken(payload) {
  return jwt.sign(payload, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN });
}

// 验证JWT token
export function verifyToken(token) {
  try {
    return jwt.verify(token, JWT_SECRET);
  } catch (error) {
    return null;
  }
}

// 从请求中获取用户信息
export function getUserFromRequest(request) {
  const authHeader = request.headers.get('authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }

  const token = authHeader.substring(7);
  return verifyToken(token);
}

// 检查用户是否有有效订阅
export function hasValidSubscription(user) {
  if (!user.subscriptions || user.subscriptions.length === 0) {
    return false;
  }

  const activeSubscription = user.subscriptions.find(
    sub => sub.status === 'active' && new Date(sub.endDate) > new Date()
  );

  return !!activeSubscription;
}

// 生成随机验证码
export function generateVerificationToken() {
  return Math.random().toString(36).substring(2, 15) +
         Math.random().toString(36).substring(2, 15);
}

// 检查是否为超级管理员
export function isSuperAdmin(user) {
  return user && user.role === 'super_admin';
}

// 验证超级管理员权限的中间件
export function requireSuperAdmin(request) {
  const tokenPayload = getUserFromRequest(request);
  if (!tokenPayload) {
    return { error: '未授权访问', status: 401 };
  }

  if (!isSuperAdmin(tokenPayload)) {
    return { error: '需要超级管理员权限', status: 403 };
  }

  return { user: tokenPayload };
}

// 邮箱格式验证
export function isValidEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// 密码强度验证
export function isValidPassword(password) {
  // 至少8位，包含字母和数字
  return password.length >= 8 && /[a-zA-Z]/.test(password) && /\d/.test(password);
}