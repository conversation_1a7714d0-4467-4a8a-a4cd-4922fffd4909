const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function createSuperAdmin() {
  try {
    console.log('🚀 开始创建超级管理员账号...');

    // 检查是否已存在超级管理员
    const existingSuperAdmin = await prisma.user.findFirst({
      where: { role: 'super_admin' }
    });

    if (existingSuperAdmin) {
      console.log('⚠️  已存在超级管理员账号:');
      console.log(`   邮箱: ${existingSuperAdmin.email}`);
      console.log(`   用户名: ${existingSuperAdmin.username || '未设置'}`);
      console.log(`   创建时间: ${existingSuperAdmin.createdAt}`);
      return;
    }

    // 超级管理员账号信息
    const superAdminData = {
      email: '<EMAIL>',
      password: 'Admin123456',
      username: 'SuperAdmin'
    };

    // 检查邮箱是否已被使用
    const existingUser = await prisma.user.findUnique({
      where: { email: superAdminData.email }
    });

    if (existingUser) {
      console.log('⚠️  邮箱已被使用，将现有用户升级为超级管理员...');
      
      const updatedUser = await prisma.user.update({
        where: { email: superAdminData.email },
        data: { 
          role: 'super_admin',
          isActive: true,
          emailVerified: true
        }
      });

      console.log('✅ 用户已升级为超级管理员:');
      console.log(`   邮箱: ${updatedUser.email}`);
      console.log(`   用户名: ${updatedUser.username || '未设置'}`);
      console.log(`   角色: ${updatedUser.role}`);
      return;
    }

    // 创建新的超级管理员账号
    const passwordHash = await bcrypt.hash(superAdminData.password, 12);

    const superAdmin = await prisma.user.create({
      data: {
        email: superAdminData.email,
        passwordHash: passwordHash,
        username: superAdminData.username,
        role: 'super_admin',
        isActive: true,
        emailVerified: true
      }
    });

    console.log('✅ 超级管理员账号创建成功!');
    console.log('📋 账号信息:');
    console.log(`   邮箱: ${superAdmin.email}`);
    console.log(`   密码: ${superAdminData.password}`);
    console.log(`   用户名: ${superAdmin.username}`);
    console.log(`   角色: ${superAdmin.role}`);
    console.log(`   ID: ${superAdmin.id}`);
    console.log('');
    console.log('⚠️  请妥善保管账号信息，建议首次登录后立即修改密码！');

  } catch (error) {
    console.error('❌ 创建超级管理员失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  createSuperAdmin();
}

module.exports = { createSuperAdmin };
