# ==============================================================================
# 饰品监控助手 - 生产环境 .gitignore 配置
# ==============================================================================

# ==============================================================================
# Node.js 依赖和包管理
# ==============================================================================
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*
.yarn/
.pnp.*
package-lock.json.bak

# ==============================================================================
# Next.js 构建和缓存文件
# ==============================================================================
.next/
out/
build/
dist/
.turbo/
*.tsbuildinfo
next-env.d.ts

# ==============================================================================
# 环境变量和配置文件 (生产环境敏感信息)
# ==============================================================================
.env
.env.local
.env.development
.env.production
.env.test
.env.*.local

# ==============================================================================
# 数据库文件 (SQLite)
# ==============================================================================
# 开发和生产数据库文件
*.db
*.db-journal
*.db-wal
*.db-shm
*.sqlite
*.sqlite3

# 数据目录
/data/
/prisma/data/
/prisma/dev.db
/prisma/prisma/

# 数据库备份文件
/backups/
*.backup
*.bak

# ==============================================================================
# 日志文件
# ==============================================================================
logs/
*.log
/logs/
combined.log
error.log
out.log
access.log

# PM2 日志
pm2.log
pm2-error.log

# ==============================================================================
# 临时文件和缓存
# ==============================================================================
.cache/
.tmp/
tmp/
temp/
*.tmp
*.temp
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# ==============================================================================
# IDE 和编辑器文件
# ==============================================================================
.vscode/
.idea/
*.swp
*.swo
*~
.project
.settings/
.classpath

# ==============================================================================
# 测试和覆盖率报告
# ==============================================================================
coverage/
.nyc_output/
.coverage
*.lcov
test-results/
junit.xml

# ==============================================================================
# 部署和运维相关
# ==============================================================================
# SSL 证书和密钥
*.pem
*.key
*.crt
*.csr
*.p12
*.pfx

# 部署配置的敏感信息
deployment-config.json
secrets.json

# 宝塔面板生成的配置
.user.ini

# ==============================================================================
# 开发工具生成的文件
# ==============================================================================
.eslintcache
.stylelintcache
.parcel-cache/

# Prisma 生成的文件
/src/generated/prisma

# ==============================================================================
# 操作系统相关
# ==============================================================================
# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon?

# Linux
*~
.nfs*

# ==============================================================================
# 压缩文件和归档
# ==============================================================================
*.zip
*.tar.gz
*.rar
*.7z

# ==============================================================================
# 项目特定的忽略文件
# ==============================================================================
# 用户上传的文件
/uploads/
/public/uploads/

# 生成的报告文件
/reports/
*.report

# 监控数据缓存
/cache/
.cache-*

# ==============================================================================
# 安全相关 - 绝对不能提交的文件
# ==============================================================================
# API 密钥和令牌
api-keys.json
tokens.json
credentials.json

# 用户数据导出
user-data-*.json
export-*.csv

# 配置备份
config.backup.*
