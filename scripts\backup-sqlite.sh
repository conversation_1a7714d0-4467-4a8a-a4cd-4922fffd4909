#!/bin/bash

# SQLite 数据库备份脚本
# 使用方法: ./scripts/backup-sqlite.sh

set -e

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

# 配置
DB_PATH="./data/production.db"
BACKUP_DIR="./backups"
DATE=$(date +"%Y%m%d_%H%M%S")
BACKUP_FILE="$BACKUP_DIR/youpin_backup_$DATE.db"

# 创建备份目录
mkdir -p $BACKUP_DIR

echo -e "${BLUE}🗄️ 开始备份 SQLite 数据库...${NC}"

# 检查数据库文件是否存在
if [ ! -f "$DB_PATH" ]; then
    echo -e "${YELLOW}⚠️ 数据库文件不存在: $DB_PATH${NC}"
    exit 1
fi

# 执行备份
echo -e "${BLUE}📋 备份文件: $BACKUP_FILE${NC}"
cp "$DB_PATH" "$BACKUP_FILE"

# 压缩备份文件
echo -e "${BLUE}🗜️ 压缩备份文件...${NC}"
gzip "$BACKUP_FILE"
COMPRESSED_FILE="$BACKUP_FILE.gz"

# 显示备份信息
BACKUP_SIZE=$(du -h "$COMPRESSED_FILE" | cut -f1)
echo -e "${GREEN}✅ 备份完成!${NC}"
echo -e "  📁 备份文件: $COMPRESSED_FILE"
echo -e "  📊 文件大小: $BACKUP_SIZE"

# 清理旧备份 (保留最近 30 天的备份)
echo -e "${BLUE}🧹 清理旧备份文件...${NC}"
find $BACKUP_DIR -name "youpin_backup_*.db.gz" -mtime +30 -delete

# 显示当前所有备份
echo -e "${BLUE}📋 当前备份列表:${NC}"
ls -lh $BACKUP_DIR/youpin_backup_*.db.gz 2>/dev/null || echo "  无备份文件"

echo -e "${GREEN}🎉 备份任务完成!${NC}"
