#!/bin/bash

# 本地部署测试脚本
# 适用于本地开发环境测试
# 使用方法: ./scripts/local-deploy.sh

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${GREEN}🚀 本地部署测试脚本${NC}"
echo "=================================================="

# 检查 Node.js 环境
check_nodejs() {
    echo -e "${BLUE}🔍 检查 Node.js 环境...${NC}"
    
    if ! command -v node &> /dev/null; then
        echo -e "${RED}❌ Node.js 未安装${NC}"
        echo -e "${YELLOW}请安装 Node.js 18+ 版本${NC}"
        exit 1
    fi
    
    NODE_VERSION=$(node -v)
    echo -e "${GREEN}✅ Node.js 版本: $NODE_VERSION${NC}"
    
    if ! command -v npm &> /dev/null; then
        echo -e "${RED}❌ npm 未安装${NC}"
        exit 1
    fi
    
    NPM_VERSION=$(npm -v)
    echo -e "${GREEN}✅ npm 版本: $NPM_VERSION${NC}"
}

# 安装依赖
install_dependencies() {
    echo -e "${BLUE}📦 安装项目依赖...${NC}"
    
    # 清理可能的缓存
    if [ -d "node_modules" ]; then
        echo -e "${YELLOW}🧹 清理旧的依赖...${NC}"
        rm -rf node_modules package-lock.json
    fi
    
    # 安装依赖
    npm install
    
    echo -e "${GREEN}✅ 依赖安装完成${NC}"
}

# 初始化数据库
init_database() {
    echo -e "${BLUE}🗄️ 初始化数据库...${NC}"
    
    # 创建数据目录
    mkdir -p data
    
    # 确保 .env 文件存在
    if [ ! -f ".env" ]; then
        echo -e "${YELLOW}📝 创建 .env 文件...${NC}"
        if [ -f ".env.production" ]; then
            cp .env.production .env
        else
            cat > .env << EOF
DATABASE_URL="file:./data/production.db"
JWT_SECRET="local-dev-jwt-secret-key-$(date +%s)"
NEXTAUTH_SECRET="local-dev-nextauth-secret-$(date +%s)"
NEXT_PUBLIC_BASE_URL="http://localhost:3000"
NEXTAUTH_URL="http://localhost:3000"
NODE_ENV="development"
EOF
        fi
    fi
    
    # 确保数据库路径正确
    if grep -q "file:./prisma/dev.db" .env; then
        echo -e "${YELLOW}📝 更新数据库路径...${NC}"
        sed -i 's|DATABASE_URL="file:./prisma/dev.db"|DATABASE_URL="file:./data/production.db"|g' .env
    fi
    
    # 生成 Prisma 客户端
    npx prisma generate
    
    # 推送数据库模式
    npx prisma db push
    
    echo -e "${GREEN}✅ 数据库初始化完成${NC}"
}

# 构建应用
build_application() {
    echo -e "${BLUE}🔨 构建应用...${NC}"
    
    # 构建应用
    if npm run build; then
        echo -e "${GREEN}✅ 应用构建完成${NC}"
    else
        echo -e "${RED}❌ 应用构建失败${NC}"
        echo -e "${YELLOW}💡 尝试解决方案:${NC}"
        echo -e "  1. 检查代码语法错误"
        echo -e "  2. 清理缓存: rm -rf .next"
        echo -e "  3. 重新安装依赖"
        exit 1
    fi
}

# 创建超级管理员
create_admin() {
    echo -e "${BLUE}👤 创建超级管理员...${NC}"
    
    if node scripts/create-super-admin.js; then
        echo -e "${GREEN}✅ 超级管理员创建完成${NC}"
    else
        echo -e "${YELLOW}⚠️ 超级管理员可能已存在${NC}"
    fi
}

# 启动应用
start_application() {
    echo -e "${BLUE}🚀 启动应用...${NC}"
    
    # 检查端口占用
    if netstat -an 2>/dev/null | grep -q ":3000 " || lsof -i :3000 2>/dev/null; then
        echo -e "${YELLOW}⚠️ 端口 3000 被占用${NC}"
        echo -e "${BLUE}尝试停止占用进程...${NC}"
        
        # Windows 环境
        if [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "win32" ]]; then
            netstat -ano | findstr :3000 | awk '{print $5}' | xargs -r taskkill /PID /F 2>/dev/null || true
        else
            # Linux/Mac 环境
            lsof -ti :3000 | xargs -r kill -9 2>/dev/null || true
        fi
        
        sleep 2
    fi
    
    echo -e "${GREEN}✅ 应用准备就绪${NC}"
    echo -e "${BLUE}🌐 访问地址: http://localhost:3000${NC}"
    echo -e "${BLUE}🔑 管理员账户: <EMAIL> / Admin123456${NC}"
    echo ""
    echo -e "${YELLOW}现在可以运行以下命令启动应用:${NC}"
    echo -e "  ${GREEN}npm start${NC}     # 生产模式"
    echo -e "  ${GREEN}npm run dev${NC}   # 开发模式"
}

# 显示结果
show_result() {
    echo ""
    echo -e "${GREEN}🎉 本地部署完成！${NC}"
    echo "=================================================="
    echo -e "${BLUE}📋 部署信息:${NC}"
    echo -e "  • 项目路径: $(pwd)"
    echo -e "  • 数据库: SQLite (./data/production.db)"
    echo -e "  • 环境配置: .env"
    echo ""
    echo -e "${BLUE}🚀 启动命令:${NC}"
    echo -e "  • 生产模式: ${YELLOW}npm start${NC}"
    echo -e "  • 开发模式: ${YELLOW}npm run dev${NC}"
    echo ""
    echo -e "${BLUE}🔧 管理命令:${NC}"
    echo -e "  • 查看数据库: ${YELLOW}npx prisma studio${NC}"
    echo -e "  • 重置数据库: ${YELLOW}npx prisma db push --force-reset${NC}"
    echo -e "  • 查看日志: 控制台输出"
    echo ""
    echo -e "${BLUE}🔑 默认管理员账户:${NC}"
    echo -e "  • 邮箱: ${YELLOW}<EMAIL>${NC}"
    echo -e "  • 密码: ${YELLOW}Admin123456${NC}"
    echo -e "  • ${RED}⚠️ 请首次登录后立即修改密码！${NC}"
    echo ""
    echo -e "${GREEN}✅ 准备就绪！可以启动应用了${NC}"
}

# 主函数
main() {
    check_nodejs
    install_dependencies
    init_database
    build_application
    create_admin
    start_application
    show_result
}

# 执行主函数
main "$@"
