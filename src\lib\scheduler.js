import { CacheService } from './cache-service.js';
import { YoupinApiService } from './youpin-api-service.js';
import { getPremiumConfig, isTemplateTargeted, isPremiumWithinLimit, calculatePremiumPercentage } from '@/data/premium-config';

/**
 * 定时任务调度器
 * 负责在半点和整点自动更新缓存数据
 */
export class Scheduler {
  static isRunning = false;
  static intervalId = null;

  /**
   * 启动定时任务
   */
  static start() {
    if (this.isRunning) {
      console.log('定时任务已在运行中');
      return;
    }

    console.log('启动定时任务调度器...');
    this.isRunning = true;

    // 立即执行一次检查
    this.checkAndExecute();

    // 每分钟检查一次是否需要执行任务
    this.intervalId = setInterval(() => {
      this.checkAndExecute();
    }, 60 * 1000); // 每分钟检查一次

    console.log('定时任务调度器已启动');
  }

  /**
   * 停止定时任务
   */
  static stop() {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
    this.isRunning = false;
    console.log('定时任务调度器已停止');
  }

  /**
   * 检查是否需要执行任务
   */
  static checkAndExecute() {
    const now = new Date();
    const minutes = now.getMinutes();

    // 在整点和半点执行任务
    if (minutes === 0 || minutes === 30) {
      console.log(`定时任务触发: ${now.toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' })}`);
      this.executeScheduledTask();
    }
  }

  /**
   * 执行定时任务
   */
  static async executeScheduledTask() {
    const taskId = await CacheService.createCacheTask('scheduled');
    
    try {
      await CacheService.updateCacheTask(taskId.id, 'running');
      console.log(`开始执行定时缓存更新任务 #${taskId.id}`);

      // 更新所有等级的缓存数据
      const tiers = ['T1', 'T2']; // 可以根据需要调整等级
      
      for (const tier of tiers) {
        try {
          console.log(`正在更新 ${tier} 等级缓存...`);
          
          // 获取悠悠API数据
          const templateData = await YoupinApiService.fetchTemplateData(tier);
          
          // 计算溢价商品
          const premiumItems = this.calculatePremiumItems(templateData.results);
          
          // 构建完整的缓存数据
          const cacheData = {
            ...templateData,
            premiumItems: premiumItems,
            premiumItemsCount: premiumItems.length,
            cachedAt: new Date().toISOString()
          };

          // 存储到缓存（30分钟过期）
          await CacheService.setCachedData(tier, 'templates', cacheData, 30);
          
          console.log(`${tier} 等级缓存更新完成，共 ${templateData.totalTemplates} 个模板，${templateData.totalItems} 个商品`);
          
        } catch (tierError) {
          console.error(`更新 ${tier} 等级缓存失败:`, tierError);
        }
      }

      await CacheService.updateCacheTask(taskId.id, 'completed');
      console.log(`定时缓存更新任务 #${taskId.id} 执行完成`);

    } catch (error) {
      console.error('定时任务执行失败:', error);
      await CacheService.updateCacheTask(taskId.id, 'failed', error.message);
    }
  }

  /**
   * 手动执行缓存更新
   * @param {string} tier - 指定等级，如果不指定则更新所有等级
   */
  static async executeManualUpdate(tier = null) {
    const taskId = await CacheService.createCacheTask('manual', tier);
    
    try {
      await CacheService.updateCacheTask(taskId.id, 'running');
      console.log(`开始执行手动缓存更新任务 #${taskId.id}`);

      const tiers = tier ? [tier] : ['T1', 'T2'];
      
      for (const currentTier of tiers) {
        try {
          console.log(`正在更新 ${currentTier} 等级缓存...`);
          
          // 获取悠悠API数据
          const templateData = await YoupinApiService.fetchTemplateData(currentTier);
          
          // 计算溢价商品
          const premiumItems = this.calculatePremiumItems(templateData.results);
          
          // 构建完整的缓存数据
          const cacheData = {
            ...templateData,
            premiumItems: premiumItems,
            premiumItemsCount: premiumItems.length,
            cachedAt: new Date().toISOString()
          };

          // 存储到缓存（30分钟过期）
          await CacheService.setCachedData(currentTier, 'templates', cacheData, 30);
          
          console.log(`${currentTier} 等级缓存更新完成`);
          
        } catch (tierError) {
          console.error(`更新 ${currentTier} 等级缓存失败:`, tierError);
          throw tierError;
        }
      }

      await CacheService.updateCacheTask(taskId.id, 'completed');
      console.log(`手动缓存更新任务 #${taskId.id} 执行完成`);
      
      return { success: true, taskId: taskId.id };

    } catch (error) {
      console.error('手动缓存更新失败:', error);
      await CacheService.updateCacheTask(taskId.id, 'failed', error.message);
      throw error;
    }
  }

  /**
   * 计算溢价商品
   * @param {Array} results - 模板结果数组
   * @returns {Array} 溢价商品数组
   */
  static calculatePremiumItems(results) {
    const premiumConfig = getPremiumConfig();
    if (!premiumConfig.enabled) {
      return [];
    }

    const premiumItems = [];

    for (const template of results) {
      // 检查是否是目标模板
      if (!isTemplateTargeted(template.templateName, premiumConfig.targetTemplates)) {
        continue;
      }

      // 检查每个商品
      for (const item of template.items) {
        const price = parseFloat(item.minSellPrice);
        const floorPrice = parseFloat(template.floorPrice);

        if (isPremiumWithinLimit(price, floorPrice, premiumConfig.maxPremiumPercentage)) {
          const premiumPercentage = calculatePremiumPercentage(price, floorPrice);
          
          premiumItems.push({
            templateName: template.templateName,
            dopplerName: template.dopplerName,
            price: price.toFixed(2),
            floorPrice: floorPrice.toFixed(2),
            premium: (price - floorPrice).toFixed(2),
            premiumPercentage: premiumPercentage,
            abrade: item.abrade,
            paintSeed: item.paintSeed,
            iconUrl: item.iconUrl
          });
        }
      }
    }

    return premiumItems;
  }

  /**
   * 获取调度器状态
   */
  static getStatus() {
    return {
      isRunning: this.isRunning,
      nextCheck: this.getNextScheduledTime(),
      lastExecution: null // 可以从数据库获取最后执行时间
    };
  }

  /**
   * 获取下次执行时间
   */
  static getNextScheduledTime() {
    const now = new Date();
    const currentMinutes = now.getMinutes();
    
    let nextMinutes;
    if (currentMinutes < 30) {
      nextMinutes = 30;
    } else {
      nextMinutes = 0;
      now.setHours(now.getHours() + 1);
    }
    
    const nextTime = new Date(now);
    nextTime.setMinutes(nextMinutes);
    nextTime.setSeconds(0);
    nextTime.setMilliseconds(0);
    
    return nextTime;
  }
}

// 在应用启动时自动启动定时任务 - 暂时禁用自动启动
// if (typeof window === 'undefined' && process.env.NODE_ENV !== 'production') { // 只在开发环境的服务端运行
//   // 延迟启动，确保应用完全初始化
//   setTimeout(() => {
//     if (!Scheduler.isRunning) { // 避免重复启动
//       Scheduler.start();
//     }
//   }, 5000);
// }
