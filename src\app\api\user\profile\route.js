import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getUserFromRequest } from '@/lib/auth';

export async function GET(request) {
  try {
    // 验证用户身份
    const tokenPayload = getUserFromRequest(request);
    if (!tokenPayload) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    // 获取用户信息
    const user = await prisma.user.findUnique({
      where: { id: tokenPayload.userId },
      include: {
        subscriptions: {
          orderBy: { createdAt: 'desc' }
        },
        payments: {
          orderBy: { createdAt: 'desc' },
          take: 10 // 最近10条支付记录
        }
      }
    });

    if (!user) {
      return NextResponse.json(
        { error: '用户不存在' },
        { status: 404 }
      );
    }

    // 检查当前活跃订阅
    const activeSubscription = user.subscriptions.find(
      sub => sub.status === 'active' && new Date(sub.endDate) > new Date()
    );

    return NextResponse.json({
      user: {
        id: user.id,
        email: user.email,
        username: user.username,
        role: user.role,
        emailVerified: user.emailVerified,
        createdAt: user.createdAt,
        subscriptions: user.subscriptions.filter(sub =>
          sub.status === 'active' && new Date(sub.endDate) > new Date()
        ),
        paymentHistory: user.payments.map(payment => ({
          id: payment.id,
          amount: payment.amount,
          currency: payment.currency,
          paymentMethod: payment.paymentMethod,
          paymentStatus: payment.paymentStatus,
          createdAt: payment.createdAt,
          completedAt: payment.completedAt
        }))
      }
    });

  } catch (error) {
    console.error('获取用户信息错误:', error);
    return NextResponse.json(
      { error: '获取用户信息失败' },
      { status: 500 }
    );
  }
}

export async function PUT(request) {
  try {
    // 验证用户身份
    const tokenPayload = getUserFromRequest(request);
    if (!tokenPayload) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    const { username } = await request.json();

    // 更新用户信息
    const updatedUser = await prisma.user.update({
      where: { id: tokenPayload.userId },
      data: {
        username: username || undefined
      }
    });

    return NextResponse.json({
      message: '用户信息更新成功',
      user: {
        id: updatedUser.id,
        email: updatedUser.email,
        username: updatedUser.username,
        role: updatedUser.role,
        emailVerified: updatedUser.emailVerified
      }
    });

  } catch (error) {
    console.error('更新用户信息错误:', error);
    return NextResponse.json(
      { error: '更新用户信息失败' },
      { status: 500 }
    );
  }
}
