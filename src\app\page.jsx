'use client';

import { useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';

export default function Home() {
  const { isAuthenticated, loading: authLoading } = useAuth();
  const router = useRouter();

  // 根据登录状态重定向
  useEffect(() => {
    if (!authLoading) {
      if (isAuthenticated) {
        router.push('/dashboard');
      } else {
        router.push('/landing'); // 未登录用户跳转到营销页面
      }
    }
  }, [authLoading, isAuthenticated, router]);

  // 显示加载状态
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 flex items-center justify-center">
      <div className="text-center bg-white rounded-2xl shadow-xl p-8 max-w-md mx-4">
        <div className="relative">
          <div className="animate-spin rounded-full h-20 w-20 border-4 border-blue-200 border-t-blue-600 mx-auto"></div>
          <div className="absolute inset-0 flex items-center justify-center">
            <span className="text-2xl">🔐</span>
          </div>
        </div>
        <h3 className="mt-6 text-xl font-semibold text-gray-800">正在跳转</h3>
        <p className="mt-2 text-gray-600">请稍候，正在检查您的登录状态...</p>
      </div>
    </div>
  );
}