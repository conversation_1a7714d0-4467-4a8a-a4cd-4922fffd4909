# 宝塔面板 Nginx 配置文件
# 将此配置复制到宝塔面板 → 网站 → 设置 → 配置文件

server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;
    
    # 网站根目录 (宝塔会自动设置)
    root /www/wwwroot/youpin-sentinel;
    index index.html index.htm index.php;
    
    # SSL 配置 (宝塔会自动添加)
    # 如果启用了 SSL，宝塔会自动添加 443 端口配置
    
    # 访问日志
    access_log /www/wwwlogs/youpin-sentinel.log;
    error_log /www/wwwlogs/youpin-sentinel.error.log;
    
    # 安全头设置
    add_header X-Frame-Options SAMEORIGIN;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    
    # Gzip 压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/json;
    
    # Next.js 静态文件缓存
    location /_next/static/ {
        alias /www/wwwroot/youpin-sentinel/.next/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }
    
    # 公共静态文件
    location /favicon.ico {
        alias /www/wwwroot/youpin-sentinel/public/favicon.ico;
        expires 30d;
        access_log off;
    }
    
    location /images/ {
        alias /www/wwwroot/youpin-sentinel/public/images/;
        expires 30d;
        add_header Cache-Control "public";
        access_log off;
    }
    
    # API 路由限流 (可选)
    location /api/ {
        # 限流配置 (需要在 nginx.conf 主配置中添加 limit_req_zone)
        # limit_req zone=api burst=20 nodelay;
        
        proxy_pass http://127.0.0.1:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # 健康检查端点
    location /health {
        proxy_pass http://127.0.0.1:3000/api/health;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        access_log off;
    }
    
    # 主应用反向代理
    location / {
        proxy_pass http://127.0.0.1:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # 错误页面
        proxy_intercept_errors on;
        error_page 502 503 504 /50x.html;
    }
    
    # 错误页面
    location = /50x.html {
        root /www/server/nginx/html;
    }
    
    # 禁止访问敏感文件
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    location ~ \.(env|log|sql)$ {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    # 禁止访问备份文件
    location ~ /backups/ {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    # 宝塔面板相关配置 (自动添加)
    include /www/server/panel/vhost/rewrite/yourdomain.com.conf;
    
    # 宝塔 SSL 配置会自动添加在这里
}
