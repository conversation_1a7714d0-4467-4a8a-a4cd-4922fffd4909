import { CacheService } from './cache-service.js';
import { YoupinApiService } from './youpin-api-service.js';
import { getPremiumConfig, isTemplateTargeted, isPremiumWithinLimit, calculatePremiumPercentage } from '@/data/premium-config';

/**
 * 启动时缓存初始化
 */
export class StartupCache {
  static isInitialized = false;
  static isInitializing = false;

  /**
   * 初始化缓存数据
   */
  static async initialize() {
    if (this.isInitialized || this.isInitializing) {
      console.log('缓存已初始化或正在初始化中...');
      return;
    }

    this.isInitializing = true;
    console.log('🚀 开始初始化缓存数据...');

    try {
      // 检查是否已有有效缓存
      const existingT1Cache = await CacheService.getCachedData('T1', 'templates');
      const existingT2Cache = await CacheService.getCachedData('T2', 'templates');

      if (existingT1Cache && existingT2Cache) {
        console.log('✅ 发现有效缓存，跳过初始化');
        this.isInitialized = true;
        this.isInitializing = false;
        return;
      }

      // 创建初始化任务
      const taskId = await CacheService.createCacheTask('startup');
      await CacheService.updateCacheTask(taskId.id, 'running');

      console.log(`📋 创建启动缓存任务 #${taskId.id}`);

      // 初始化所有等级的缓存
      const tiers = ['T1', 'T2'];
      
      for (const tier of tiers) {
        try {
          console.log(`🔄 正在初始化 ${tier} 等级缓存...`);
          
          // 获取悠悠API数据
          const templateData = await YoupinApiService.fetchTemplateData(tier);
          
          // 计算溢价商品
          const premiumItems = this.calculatePremiumItems(templateData.results);
          
          // 构建完整的缓存数据
          const cacheData = {
            ...templateData,
            premiumItems: premiumItems,
            premiumItemsCount: premiumItems.length,
            cachedAt: new Date().toISOString(),
            initializedAt: new Date().toISOString()
          };

          // 存储到缓存（30分钟过期）
          await CacheService.setCachedData(tier, 'templates', cacheData, 30);
          
          console.log(`✅ ${tier} 等级缓存初始化完成，共 ${templateData.totalTemplates} 个模板，${templateData.totalItems} 个商品`);
          
        } catch (tierError) {
          console.error(`❌ 初始化 ${tier} 等级缓存失败:`, tierError);
        }
      }

      await CacheService.updateCacheTask(taskId.id, 'completed');
      console.log(`🎉 启动缓存初始化任务 #${taskId.id} 执行完成`);

      this.isInitialized = true;

    } catch (error) {
      console.error('❌ 启动缓存初始化失败:', error);
      if (taskId) {
        await CacheService.updateCacheTask(taskId.id, 'failed', error.message);
      }
    } finally {
      this.isInitializing = false;
    }
  }

  /**
   * 计算溢价商品
   * @param {Array} results - 模板结果数组
   * @returns {Array} 溢价商品数组
   */
  static calculatePremiumItems(results) {
    try {
      const premiumConfig = getPremiumConfig();
      if (!premiumConfig.enabled) {
        return [];
      }

      const premiumItems = [];

      for (const template of results) {
        // 检查是否是目标模板
        if (!isTemplateTargeted(template.templateName, premiumConfig.targetTemplates)) {
          continue;
        }

        // 检查每个商品
        for (const item of template.items) {
          const price = parseFloat(item.minSellPrice);
          const floorPrice = parseFloat(template.floorPrice);

          if (isPremiumWithinLimit(price, floorPrice, premiumConfig.maxPremiumPercentage)) {
            const premiumPercentage = calculatePremiumPercentage(price, floorPrice);
            
            premiumItems.push({
              templateName: template.templateName,
              dopplerName: template.dopplerName,
              price: price.toFixed(2),
              floorPrice: floorPrice.toFixed(2),
              premium: (price - floorPrice).toFixed(2),
              premiumPercentage: premiumPercentage,
              abrade: item.abrade,
              paintSeed: item.paintSeed,
              iconUrl: item.iconUrl
            });
          }
        }
      }

      return premiumItems;
    } catch (error) {
      console.error('计算溢价商品失败:', error);
      return [];
    }
  }
}

// 在服务端启动时自动初始化缓存 - 暂时禁用自动启动
// if (typeof window === 'undefined') { // 只在服务端运行
//   // 延迟启动，确保应用完全初始化
//   setTimeout(() => {
//     StartupCache.initialize().catch(console.error);
//   }, 3000); // 3秒后启动
// }
