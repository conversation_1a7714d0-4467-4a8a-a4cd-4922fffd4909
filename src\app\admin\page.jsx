'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';

export default function AdminPage() {
  const [stats, setStats] = useState(null);
  const [loading, setLoading] = useState(true);
  const [checking, setChecking] = useState(false);

  useEffect(() => {
    checkSubscriptionStatus();
  }, []);

  const checkSubscriptionStatus = async () => {
    setChecking(true);
    try {
      const response = await fetch('/api/subscription/check');
      const data = await response.json();
      setStats(data.data);
    } catch (error) {
      console.error('检查订阅状态失败:', error);
    } finally {
      setLoading(false);
      setChecking(false);
    }
  };

  const sendRenewalReminders = async () => {
    try {
      const response = await fetch('/api/subscription/check', {
        method: 'POST'
      });
      const data = await response.json();
      alert(`续费提醒发送完成，共发送 ${data.data.notificationsSent} 条通知`);
    } catch (error) {
      alert('发送续费提醒失败: ' + error.message);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-4 border-blue-600 border-t-transparent"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-6xl mx-auto">
        {/* 头部 */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-4">
            系统管理
          </h1>
          <Link href="/dashboard">
            <button className="text-blue-600 hover:text-blue-500 transition-colors">
              ← 返回仪表板
            </button>
          </Link>
        </div>

        {/* 统计卡片 */}
        <div className="grid md:grid-cols-3 gap-6 mb-8">
          <div className="bg-white rounded-xl shadow-lg p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">即将过期订阅</p>
                <p className="text-2xl font-bold text-orange-600">{stats?.soonToExpire || 0}</p>
              </div>
              <div className="text-3xl">⏰</div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-lg p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">已过期订阅</p>
                <p className="text-2xl font-bold text-red-600">{stats?.expired || 0}</p>
              </div>
              <div className="text-3xl">❌</div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-lg p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">系统状态</p>
                <p className="text-lg font-bold text-green-600">正常运行</p>
              </div>
              <div className="text-3xl">✅</div>
            </div>
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="bg-white rounded-xl shadow-lg p-6 mb-8">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">系统操作</h2>
          <div className="flex space-x-4">
            <button
              onClick={checkSubscriptionStatus}
              disabled={checking}
              className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
            >
              {checking ? '检查中...' : '🔄 检查订阅状态'}
            </button>
            
            <button
              onClick={sendRenewalReminders}
              className="px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
            >
              📧 发送续费提醒
            </button>
          </div>
        </div>

        {/* 即将过期的订阅 */}
        {stats?.soonToExpireSubscriptions && stats.soonToExpireSubscriptions.length > 0 && (
          <div className="bg-white rounded-xl shadow-lg p-6 mb-8">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">即将过期的订阅</h2>
            <div className="overflow-x-auto">
              <table className="w-full text-sm">
                <thead>
                  <tr className="border-b border-gray-200">
                    <th className="text-left py-3 px-4">用户邮箱</th>
                    <th className="text-left py-3 px-4">订阅类型</th>
                    <th className="text-left py-3 px-4">到期时间</th>
                    <th className="text-left py-3 px-4">剩余天数</th>
                  </tr>
                </thead>
                <tbody>
                  {stats.soonToExpireSubscriptions.map((sub, index) => (
                    <tr key={index} className="border-b border-gray-100">
                      <td className="py-3 px-4">{sub.userEmail}</td>
                      <td className="py-3 px-4">
                        {sub.planType === 'monthly' ? '月度会员' : '年度会员'}
                      </td>
                      <td className="py-3 px-4">
                        {new Date(sub.endDate).toLocaleDateString()}
                      </td>
                      <td className="py-3 px-4">
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                          sub.daysRemaining <= 1 
                            ? 'bg-red-100 text-red-800' 
                            : sub.daysRemaining <= 3
                            ? 'bg-orange-100 text-orange-800'
                            : 'bg-yellow-100 text-yellow-800'
                        }`}>
                          {sub.daysRemaining} 天
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {/* 已过期的订阅 */}
        {stats?.expiredSubscriptions && stats.expiredSubscriptions.length > 0 && (
          <div className="bg-white rounded-xl shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">已过期的订阅</h2>
            <div className="overflow-x-auto">
              <table className="w-full text-sm">
                <thead>
                  <tr className="border-b border-gray-200">
                    <th className="text-left py-3 px-4">用户邮箱</th>
                    <th className="text-left py-3 px-4">订阅类型</th>
                    <th className="text-left py-3 px-4">过期时间</th>
                  </tr>
                </thead>
                <tbody>
                  {stats.expiredSubscriptions.map((sub, index) => (
                    <tr key={index} className="border-b border-gray-100">
                      <td className="py-3 px-4">{sub.userEmail}</td>
                      <td className="py-3 px-4">
                        {sub.planType === 'monthly' ? '月度会员' : '年度会员'}
                      </td>
                      <td className="py-3 px-4">
                        {new Date(sub.endDate).toLocaleDateString()}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {/* 使用说明 */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-xl p-6 mt-8">
          <h3 className="text-lg font-semibold text-yellow-800 mb-2">使用说明</h3>
          <ul className="text-sm text-yellow-700 space-y-1">
            <li>• 系统会自动检查订阅状态并更新过期订阅</li>
            <li>• 可以手动发送续费提醒给即将过期的用户</li>
            <li>• 建议设置定时任务每天运行订阅状态检查</li>
            <li>• 在生产环境中，可以集成邮件服务发送真实的续费提醒</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
