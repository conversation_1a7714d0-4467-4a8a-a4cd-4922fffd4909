# 生产环境配置文件模板
# 复制此文件为 .env.production 并填入真实的生产环境值

# ===========================================
# 数据库配置 (使用 SQLite)
# ===========================================

# SQLite 数据库文件路径 (生产环境)
DATABASE_URL="file:./data/production.db"

# ===========================================
# 安全配置
# ===========================================

# JWT 密钥 - 必须是强密码，建议使用 openssl rand -base64 32 生成
JWT_SECRET="your-super-secure-jwt-secret-key-change-this-in-production"

# NextAuth 配置
NEXTAUTH_SECRET="your-nextauth-secret-key-change-this-in-production"
NEXTAUTH_URL="https://yourdomain.com"

# ===========================================
# 应用配置
# ===========================================

# 应用基础URL (生产环境域名)
NEXT_PUBLIC_BASE_URL="https://yourdomain.com"

# Node.js 环境
NODE_ENV="production"

# ===========================================
# 支付配置 (如果使用真实支付)
# ===========================================

# 支付宝配置
# ALIPAY_APP_ID="your_alipay_app_id"
# ALIPAY_PRIVATE_KEY="your_alipay_private_key"
# ALIPAY_PUBLIC_KEY="alipay_public_key"

# 微信支付配置
# WECHAT_APP_ID="your_wechat_app_id"
# WECHAT_MCH_ID="your_wechat_mch_id"
# WECHAT_API_KEY="your_wechat_api_key"

# ===========================================
# 邮件服务配置 (用于发送通知邮件)
# ===========================================

# SMTP 配置
# SMTP_HOST="smtp.gmail.com"
# SMTP_PORT="587"
# SMTP_USER="<EMAIL>"
# SMTP_PASS="your-app-password"

# ===========================================
# 企业微信机器人 (用于价格提醒)
# ===========================================

# 企业微信机器人 Webhook URL
# WECHAT_WORK_WEBHOOK="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=your-key"

# ===========================================
# 日志和监控
# ===========================================

# 日志级别
LOG_LEVEL="info"

# 是否启用详细日志
ENABLE_DETAILED_LOGS="false"

# ===========================================
# 性能配置
# ===========================================

# 数据库连接池大小
DATABASE_POOL_SIZE="10"

# API 请求限制 (每分钟)
API_RATE_LIMIT="100"
