// 支付系统工具

/**
 * 生成订单号
 * @returns {string} 订单号
 */
export function generateOrderNo() {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 8);
  return `YP${timestamp}${random}`;
}

/**
 * 验证支付金额是否有效
 * @param {number} amount - 支付金额
 * @returns {boolean} 是否有效
 */
export function isValidPaymentAmount(amount) {
  const validAmounts = [20, 120]; // 月费和年费
  return validAmounts.includes(amount);
}

/**
 * 格式化支付金额
 * @param {number} amount - 金额
 * @returns {string} 格式化后的金额
 */
export function formatPaymentAmount(amount) {
  return (Math.round(amount * 100) / 100).toFixed(2);
}

/**
 * 获取支付方式显示名称
 * @param {string} method - 支付方式代码
 * @returns {string} 显示名称
 */
export function getPaymentMethodName(method) {
  const names = {
    manual: '手动确认',
    wechat: '微信支付',
    alipay: '支付宝'
  };
  return names[method] || '未知支付方式';
}

/**
 * 获取支付状态显示名称
 * @param {string} status - 支付状态代码
 * @returns {string} 显示名称
 */
export function getPaymentStatusName(status) {
  const names = {
    pending: '待支付',
    completed: '已完成',
    failed: '支付失败',
    cancelled: '已取消'
  };
  return names[status] || '未知状态';
}
