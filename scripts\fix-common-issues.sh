#!/bin/bash

# 常见问题修复脚本
# 用于快速解决部署和运行中的常见问题
# 使用方法: ./scripts/fix-common-issues.sh

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${GREEN}🔧 常见问题修复工具${NC}"
echo "=================================================="

# 显示菜单
show_menu() {
    echo -e "${BLUE}请选择要修复的问题:${NC}"
    echo "1. 修复数据库连接问题"
    echo "2. 修复端口占用问题"
    echo "3. 修复环境变量配置"
    echo "4. 重新构建应用"
    echo "5. 重置数据库"
    echo "6. 修复文件权限 (Linux/Mac)"
    echo "7. 清理缓存和重新安装"
    echo "8. 检查系统状态"
    echo "9. 全部修复"
    echo "0. 退出"
    echo ""
    read -p "请输入选项 (0-9): " choice
}

# 修复数据库连接问题
fix_database() {
    echo -e "${BLUE}🗄️ 修复数据库连接问题...${NC}"
    
    # 创建数据目录
    mkdir -p data
    
    # 检查并修复 .env 文件
    if [ ! -f ".env" ]; then
        echo -e "${YELLOW}📝 创建 .env 文件...${NC}"
        if [ -f ".env.production" ]; then
            cp .env.production .env
        else
            echo 'DATABASE_URL="file:./data/production.db"' > .env
        fi
    fi
    
    # 确保数据库路径正确
    if grep -q "file:./prisma/dev.db" .env; then
        echo -e "${YELLOW}📝 更新数据库路径...${NC}"
        sed -i 's|DATABASE_URL="file:./prisma/dev.db"|DATABASE_URL="file:./data/production.db"|g' .env
    fi
    
    # 重新生成 Prisma 客户端
    npx prisma generate
    
    # 推送数据库模式
    npx prisma db push
    
    echo -e "${GREEN}✅ 数据库问题已修复${NC}"
}

# 修复端口占用问题
fix_port() {
    echo -e "${BLUE}🔌 修复端口占用问题...${NC}"
    
    # 检查端口占用
    if netstat -an 2>/dev/null | grep -q ":3000 " || lsof -i :3000 2>/dev/null; then
        echo -e "${YELLOW}⚠️ 发现端口 3000 被占用${NC}"
        
        # Windows 环境
        if [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "win32" ]]; then
            echo -e "${BLUE}停止占用端口的进程...${NC}"
            netstat -ano | findstr :3000
            echo -e "${YELLOW}请手动停止上述进程，或运行: taskkill /PID <PID> /F${NC}"
        else
            # Linux/Mac 环境
            echo -e "${BLUE}停止占用端口的进程...${NC}"
            lsof -ti :3000 | xargs -r kill -9 2>/dev/null || true
            echo -e "${GREEN}✅ 端口已释放${NC}"
        fi
    else
        echo -e "${GREEN}✅ 端口 3000 未被占用${NC}"
    fi
    
    # 停止 PM2 进程
    if command -v pm2 &> /dev/null; then
        echo -e "${BLUE}停止 PM2 进程...${NC}"
        pm2 stop youpin-sentinel 2>/dev/null || true
        pm2 delete youpin-sentinel 2>/dev/null || true
    fi
}

# 修复环境变量配置
fix_env() {
    echo -e "${BLUE}⚙️ 修复环境变量配置...${NC}"
    
    # 备份现有配置
    if [ -f ".env" ]; then
        cp .env .env.backup.$(date +%s)
        echo -e "${BLUE}已备份现有 .env 文件${NC}"
    fi
    
    # 创建新的配置文件
    cat > .env << EOF
# 数据库配置
DATABASE_URL="file:./data/production.db"

# JWT 密钥 (生产环境请修改)
JWT_SECRET="$(openssl rand -base64 32 2>/dev/null || echo "fallback-jwt-secret-$(date +%s)")"

# NextAuth 配置
NEXTAUTH_SECRET="$(openssl rand -base64 32 2>/dev/null || echo "fallback-nextauth-secret-$(date +%s)")"
NEXTAUTH_URL="http://localhost:3000"

# 应用配置
NEXT_PUBLIC_BASE_URL="http://localhost:3000"
NODE_ENV="production"

# 日志配置
LOG_LEVEL="info"
EOF
    
    echo -e "${GREEN}✅ 环境变量配置已修复${NC}"
}

# 重新构建应用
rebuild_app() {
    echo -e "${BLUE}🔨 重新构建应用...${NC}"
    
    # 清理构建缓存
    rm -rf .next
    
    # 重新构建
    npm run build
    
    echo -e "${GREEN}✅ 应用重新构建完成${NC}"
}

# 重置数据库
reset_database() {
    echo -e "${BLUE}🗄️ 重置数据库...${NC}"
    echo -e "${RED}⚠️ 这将删除所有数据！${NC}"
    read -p "确认重置数据库? (y/N): " confirm
    
    if [[ "$confirm" =~ ^[Yy]$ ]]; then
        # 删除数据库文件
        rm -f data/production.db*
        rm -f prisma/dev.db*
        
        # 重新初始化
        mkdir -p data
        npx prisma db push --force-reset
        
        # 重新创建管理员
        node scripts/create-super-admin.js
        
        echo -e "${GREEN}✅ 数据库已重置${NC}"
    else
        echo -e "${YELLOW}取消重置${NC}"
    fi
}

# 修复文件权限
fix_permissions() {
    echo -e "${BLUE}🔐 修复文件权限...${NC}"
    
    if [[ "$OSTYPE" == "linux-gnu"* ]] || [[ "$OSTYPE" == "darwin"* ]]; then
        # 设置项目文件权限
        find . -type f -name "*.sh" -exec chmod +x {} \;
        chmod -R 755 scripts/
        
        # 设置数据目录权限
        if [ -d "data" ]; then
            chmod -R 755 data/
        fi
        
        echo -e "${GREEN}✅ 文件权限已修复${NC}"
    else
        echo -e "${YELLOW}⚠️ Windows 环境无需修复权限${NC}"
    fi
}

# 清理缓存和重新安装
clean_install() {
    echo -e "${BLUE}🧹 清理缓存和重新安装...${NC}"
    
    # 清理缓存
    rm -rf node_modules
    rm -f package-lock.json
    rm -rf .next
    
    # 重新安装依赖
    npm install
    
    # 重新生成 Prisma 客户端
    npx prisma generate
    
    echo -e "${GREEN}✅ 清理和重新安装完成${NC}"
}

# 检查系统状态
check_status() {
    echo -e "${BLUE}📊 检查系统状态...${NC}"
    echo ""
    
    # Node.js 版本
    echo -e "${BLUE}Node.js 版本:${NC}"
    node -v 2>/dev/null || echo "未安装"
    echo ""
    
    # npm 版本
    echo -e "${BLUE}npm 版本:${NC}"
    npm -v 2>/dev/null || echo "未安装"
    echo ""
    
    # 项目文件检查
    echo -e "${BLUE}项目文件检查:${NC}"
    [ -f "package.json" ] && echo "✅ package.json" || echo "❌ package.json"
    [ -f ".env" ] && echo "✅ .env" || echo "❌ .env"
    [ -f "prisma/schema.prisma" ] && echo "✅ prisma/schema.prisma" || echo "❌ prisma/schema.prisma"
    [ -d "data" ] && echo "✅ data/" || echo "❌ data/"
    [ -f "data/production.db" ] && echo "✅ 数据库文件" || echo "❌ 数据库文件"
    echo ""
    
    # 端口检查
    echo -e "${BLUE}端口检查:${NC}"
    if netstat -an 2>/dev/null | grep -q ":3000 " || lsof -i :3000 2>/dev/null; then
        echo "❌ 端口 3000 被占用"
    else
        echo "✅ 端口 3000 可用"
    fi
    echo ""
    
    # PM2 状态
    if command -v pm2 &> /dev/null; then
        echo -e "${BLUE}PM2 状态:${NC}"
        pm2 list 2>/dev/null || echo "PM2 未运行"
    else
        echo -e "${BLUE}PM2:${NC} 未安装"
    fi
    echo ""
}

# 全部修复
fix_all() {
    echo -e "${BLUE}🔧 执行全部修复...${NC}"
    
    fix_port
    fix_env
    fix_database
    clean_install
    rebuild_app
    fix_permissions
    
    echo -e "${GREEN}✅ 全部修复完成${NC}"
}

# 主函数
main() {
    while true; do
        show_menu
        
        case $choice in
            1) fix_database ;;
            2) fix_port ;;
            3) fix_env ;;
            4) rebuild_app ;;
            5) reset_database ;;
            6) fix_permissions ;;
            7) clean_install ;;
            8) check_status ;;
            9) fix_all ;;
            0) echo -e "${GREEN}退出修复工具${NC}"; exit 0 ;;
            *) echo -e "${RED}无效选项，请重新选择${NC}" ;;
        esac
        
        echo ""
        read -p "按回车键继续..." 
        echo ""
    done
}

# 执行主函数
main "$@"
