import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function GET() {
  try {
    // 检查数据库连接
    await prisma.$queryRaw`SELECT 1`;
    
    // 获取系统状态
    const userCount = await prisma.user.count();
    const activeSubscriptions = await prisma.subscription.count({
      where: {
        status: 'active',
        endDate: {
          gt: new Date()
        }
      }
    });
    
    return NextResponse.json({ 
      status: 'ok',
      timestamp: new Date().toISOString(),
      database: 'connected',
      stats: {
        totalUsers: userCount,
        activeSubscriptions: activeSubscriptions
      },
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development'
    });
  } catch (error) {
    console.error('Health check failed:', error);
    return NextResponse.json(
      { 
        status: 'error',
        timestamp: new Date().toISOString(),
        database: 'disconnected',
        error: error.message
      },
      { status: 503 }
    );
  }
}
