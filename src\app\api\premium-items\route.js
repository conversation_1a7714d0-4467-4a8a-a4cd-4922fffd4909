import { NextRequest, NextResponse } from 'next/server';
import { getPremiumConfig, isTemplateTargeted, isPremiumWithinLimit, calculatePremiumPercentage } from '@/data/premium-config';

// 企业微信机器人配置
const WECHAT_WEBHOOK_URL = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=a87de667-269b-4939-b9f2-81c9f88bce9b';

// 发送溢价商品通知
async function sendPremiumItemsNotification(premiumItems, maxPremiumPercentage = 0.1) {
  const maxPercentage = (maxPremiumPercentage * 100).toFixed(0);

  if (premiumItems.length === 0) {
    // 发送无符合条件商品的通知
    const message = {
      msgtype: 'markdown',
      markdown: {
        content: `## 📊 目标商品溢价查询结果

**查询时间**: ${new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' })}
**溢价限制**: ≤ ${maxPercentage}%
**筛选范围**: 仅配置的目标商品

### 📋 查询结果
暂无符合条件的商品

---
*溢价% = (当前价格 - 底价) / 底价 × 100%*`
      }
    };

    const response = await fetch(WECHAT_WEBHOOK_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(message)
    });

    return {
      success: response.ok,
      message: '无符合条件商品通知已发送'
    };
  }

  try {
    // 按溢价百分比从低到高排序
    const sortedItems = premiumItems.sort((a, b) => parseFloat(a.premium) - parseFloat(b.premium));

    // 构建商品列表，每条消息最多显示15个商品
    const itemsToShow = sortedItems.slice(0, 15);

    let itemsList = '';
    itemsToShow.forEach((item, index) => {
      itemsList += `**${index + 1}. ${item.templateName}**\n`;
      itemsList += `🔸 **模板**: ${item.dopplerName}\n`;
      itemsList += `🔸 **磨损**: ${item.abrade}\n`;
      itemsList += `🔸 **价格**: ¥${item.price}\n`;
      itemsList += `🔸 **溢价**: ¥${item.premium} (${item.premiumPercentage})\n`;
      itemsList += `🔸 **底价**: ¥${item.floorPrice}\n\n`;
    });

    const message = {
      msgtype: 'markdown',
      markdown: {
        content: `## 💎 发现溢价${maxPercentage}%以内的目标商品！

**查询时间**: ${new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' })}
**符合条件商品**: ${premiumItems.length}个
**溢价限制**: ≤ ${maxPercentage}%
**筛选范围**: 仅配置的目标商品

### 📋 商品详情 (按溢价排序)
${itemsList}

${premiumItems.length > 15 ? `**还有${premiumItems.length - 15}个商品未显示...**\n\n` : ''}

### 🔗 查看更多
[点击查看完整列表](http://60.205.154.102:3000/)

---
*溢价% = (当前价格 - 底价) / 底价 × 100%*
*仅筛选配置的目标商品*`
      }
    };

    const response = await fetch(WECHAT_WEBHOOK_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(message)
    });

    return {
      success: response.ok,
      message: `溢价商品通知发送${response.ok ? '成功' : '失败'}，共${premiumItems.length}个商品`
    };
  } catch (error) {
    return {
      success: false,
      message: '发送溢价商品通知时出错: ' + error.message
    };
  }
}

export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const tier = searchParams.get('tier') || 'T1';

    // 获取配置
    const premiumConfig = getPremiumConfig();
    if (!premiumConfig.enabled) {
      return NextResponse.json({
        code: 0,
        msg: '溢价筛选功能已禁用',
        timestamp: Date.now(),
        data: {
          tier: tier,
          premiumItems: [],
          premiumItemsCount: 0,
          configEnabled: false
        }
      });
    }

    // 调用主查询接口获取数据
    const baseUrl = request.url.replace('/api/premium-items', '/api/templates');
    const templatesUrl = new URL(baseUrl);
    templatesUrl.searchParams.set('tier', tier);

    const response = await fetch(templatesUrl.toString(), {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      }
    });

    if (!response.ok) {
      throw new Error(`查询模板数据失败: ${response.status}`);
    }

    const data = await response.json();

    if (data.code !== 0) {
      throw new Error(data.msg || '查询失败');
    }

    // 筛选配置的商品中溢价10%以内的商品
    const premiumItems = [];
    data.data.results.forEach((result) => {
      // 检查是否是目标模板
      if (!isTemplateTargeted(result.templateName)) {
        return; // 跳过非目标模板
      }

      if (result.floorPrice && result.floorPrice > 0) {
        const floorPrice = result.floorPrice;
        result.items.forEach((item) => {
          const currentPrice = parseFloat(item.minSellPrice);

          // 使用新的百分比逻辑检查溢价
          if (isPremiumWithinLimit(currentPrice, floorPrice)) {
            const premiumAmount = currentPrice - floorPrice;
            const premiumPercentage = calculatePremiumPercentage(currentPrice, floorPrice);

            premiumItems.push({
              templateName: result.templateName,
              dopplerName: result.dopplerName,
              price: currentPrice.toFixed(2),
              premium: premiumAmount.toFixed(2),
              premiumPercentage: (premiumPercentage * 100).toFixed(2) + '%',
              abrade: item.abrade,
              paintSeed: item.paintSeed,
              floorPrice: floorPrice.toFixed(2)
            });
          }
        });
      }
    });

    // 发送企业微信通知
    const notificationResult = await sendPremiumItemsNotification(premiumItems, premiumConfig.maxPremiumPercentage);

    return NextResponse.json({
      code: 0,
      msg: '查询完成',
      timestamp: Date.now(),
      data: {
        tier: tier,
        maxPremiumPercentage: (premiumConfig.maxPremiumPercentage * 100).toFixed(0) + '%',
        targetTemplatesCount: premiumConfig.targetTemplates.length,
        premiumItems: premiumItems,
        premiumItemsCount: premiumItems.length,
        notificationSent: notificationResult.success,
        notificationMessage: notificationResult.message
      }
    });

  } catch (error) {
    return NextResponse.json({
      code: -1,
      msg: '查询失败: ' + error.message,
      timestamp: Date.now(),
      data: null
    }, { status: 500 });
  }
}
